﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="scr\other_tools.cpp" />
    <ClCompile Include="capture\capture.cpp" />
    <ClCompile Include="overlay\overlay.cpp" />
    <ClCompile Include="mouse\ghub.cpp" />
    <ClCompile Include="mouse\mouse.cpp" />
    <ClCompile Include="mouse\SerialConnection.cpp" />
    <ClCompile Include="mouse\AimbotTarget.cpp" />
    <ClCompile Include="detector\detector.cpp" />
    <ClCompile Include="tensorrt\nvinf.cpp" />
    <ClCompile Include="keyboard\keyboard_listener.cpp" />
    <ClCompile Include="keyboard\keycodes.cpp" />
    <ClCompile Include="config\config.cpp" />
    <ClCompile Include="imgui\imgui.cpp" />
    <ClCompile Include="imgui\imgui_draw.cpp" />
    <ClCompile Include="imgui\imgui_impl_dx11.cpp" />
    <ClCompile Include="imgui\imgui_impl_win32.cpp" />
    <ClCompile Include="imgui\imgui_tables.cpp" />
    <ClCompile Include="imgui\imgui_widgets.cpp" />
    <ClCompile Include="detector\postProcess.cpp" />
    <ClCompile Include="capture\winrt_capture.cpp" />
    <ClCompile Include="capture\duplication_api_capture.cpp" />
    <ClCompile Include="capture\optical_flow.cpp" />
    <ClCompile Include="capture\virtual_camera.cpp" />
    <ClCompile Include="sunone_aimbot_cpp.cpp" />
    <ClCompile Include="overlay\draw_capture.cpp" />
    <ClCompile Include="overlay\draw_target.cpp" />
    <ClCompile Include="overlay\draw_mouse.cpp" />
    <ClCompile Include="overlay\draw_ai.cpp" />
    <ClCompile Include="overlay\draw_optical_flow.cpp" />
    <ClCompile Include="overlay\draw_buttons.cpp" />
    <ClCompile Include="overlay\draw_overlay.cpp" />
    <ClCompile Include="overlay\draw_debug.cpp" />
    <ClCompile Include="mouse\Kmbox_b.cpp" />
    <ClCompile Include="detector\directml_detector.cpp" />
    <ClCompile Include="overlay\draw_stats.cpp" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="scr">
      <UniqueIdentifier>{0520f04b-237c-4399-a138-0b134198428c}</UniqueIdentifier>
    </Filter>
    <Filter Include="include">
      <UniqueIdentifier>{5a87bd36-3f47-4f8f-862a-c2d9ac0ec128}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\other_tools.h" />
    <ClInclude Include="include\memory_images.h" />
    <ClInclude Include="capture\capture.h" />
    <ClInclude Include="overlay\overlay.h" />
    <ClInclude Include="mouse\ghub.h" />
    <ClInclude Include="mouse\mouse.h" />
    <ClInclude Include="mouse\SerialConnection.h" />
    <ClInclude Include="mouse\AimbotTarget.h" />
    <ClInclude Include="detector\detector.h" />
    <ClInclude Include="tensorrt\nvinf.h" />
    <ClInclude Include="keyboard\keyboard_listener.h" />
    <ClInclude Include="keyboard\keycodes.h" />
    <ClInclude Include="config\config.h" />
    <ClInclude Include="imgui\imconfig.h" />
    <ClInclude Include="imgui\imgui.h" />
    <ClInclude Include="imgui\imgui_impl_dx11.h" />
    <ClInclude Include="imgui\imgui_impl_win32.h" />
    <ClInclude Include="imgui\imgui_internal.h" />
    <ClInclude Include="imgui\imstb_rectpack.h" />
    <ClInclude Include="imgui\imstb_textedit.h" />
    <ClInclude Include="imgui\imstb_truetype.h" />
    <ClInclude Include="detector\postProcess.h" />
    <ClInclude Include="capture\winrt_capture.h" />
    <ClInclude Include="capture\duplication_api_capture.h" />
    <ClInclude Include="capture\optical_flow.h" />
    <ClInclude Include="capture\virtual_camera.h" />
    <ClInclude Include="sunone_aimbot_cpp.h" />
    <ClInclude Include="overlay\draw_settings.h" />
    <ClInclude Include="mouse\Kmbox_b.h" />
    <ClInclude Include="detector\directml_detector.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <AppxManifest Include="Package.appxmanifest" />
  </ItemGroup>
</Project>